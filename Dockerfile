FROM node:22-alpine AS base
# Install pnpm globally once
RUN npm install -g pnpm

FROM base AS development-dependencies-env
WORKDIR /app
# Copy package files first for better caching
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

FROM base AS production-dependencies-env
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile --prod

FROM base AS build-env
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
COPY --from=development-dependencies-env /app/node_modules ./node_modules
# Copy source code
COPY . .
RUN pnpm run build

FROM base AS runtime
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
COPY --from=production-dependencies-env /app/node_modules ./node_modules
COPY --from=build-env /app/build ./build
EXPOSE 4000
CMD ["pnpm", "run", "start"]