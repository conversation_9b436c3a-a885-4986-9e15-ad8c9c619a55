import { Link } from "react-router"
import clsx from "clsx"
import React from "react"

type ButtonProps = {
  invert?: boolean
  href?: string
} & (React.ComponentPropsWithoutRef<typeof Link> | React.ComponentPropsWithoutRef<"button">)

export function Button({ invert = false, className, children, ...props }: ButtonProps) {
  className = clsx(
    className,
    "inline-flex rounded-full px-4 py-1.5 text-sm font-semibold transition",
    invert ? "bg-white text-neutral-950 hover:bg-neutral-200" : "bg-neutral-950 text-white hover:bg-neutral-800",
  )

  let inner = <span className="relative top-px">{children}</span>

  if (typeof props.href === "undefined") {
    return (
      <button className={className} {...(props as React.ComponentPropsWithoutRef<"button">)}>
        {inner}
      </button>
    )
  }

  return (
    <Link className={className} {...(props as React.ComponentPropsWithoutRef<typeof Link>)} to={props.href}>
      {inner}
    </Link>
  )
}
