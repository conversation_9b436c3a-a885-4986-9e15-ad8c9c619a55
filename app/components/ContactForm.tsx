import { type ComponentProps, useId, useState } from "react"
import { FadeIn } from "@/components/FadeIn"
import { Button } from "@/components/Button"
import { useNavigate } from "react-router"
import { type SubmitHandler, useForm } from "react-hook-form"
import { supabase } from "@/lib/supabase"

function TextInput({ label, error, ...props }: ComponentProps<"input"> & { label: string; error?: string }) {
  const id = useId()

  return (
    <div className="group relative z-0 transition-all focus-within:z-10">
      <input
        type="text"
        id={id}
        {...props}
        placeholder=" "
        className="peer block w-full border border-neutral-300 bg-transparent px-6 pb-4 pt-12 text-base/6 text-neutral-950 ring-4 ring-transparent transition focus:border-neutral-950 focus:outline-none focus:ring-neutral-950/5 group-first:rounded-t-2xl group-last:rounded-b-2xl"
      />
      <label
        htmlFor={id}
        className="pointer-events-none absolute left-6 top-1/2 -mt-3 origin-left text-base/6 text-neutral-500 transition-all duration-200 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:font-semibold peer-focus:text-neutral-950 peer-[:not(:placeholder-shown)]:-translate-y-4 peer-[:not(:placeholder-shown)]:scale-75 peer-[:not(:placeholder-shown)]:font-semibold peer-[:not(:placeholder-shown)]:text-neutral-950"
      >
        {label}
        {!error ? null : <p className="text-xs text-red-700">{error}</p>}
      </label>
    </div>
  )
}

function RadioInput({ label, ...props }: ComponentProps<"input"> & { label: string }) {
  return (
    <label className="flex gap-x-3">
      <input
        type="radio"
        {...props}
        className="h-6 w-6 flex-none appearance-none rounded-full border border-neutral-950/20 outline-none checked:border-[0.5rem] checked:border-neutral-950 focus-visible:ring-1 focus-visible:ring-neutral-950 focus-visible:ring-offset-2"
      />
      <span className="text-base/6 text-neutral-950">{label}</span>
    </label>
  )
}

type Inputs = {
  name: string
  email: string
  country?: string
  phone?: string
  preference?: string
  message?: string
}

export function ContactForm() {
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<Inputs>()

  const onSubmit: SubmitHandler<Inputs> = async (data) => {
    setIsSubmitting(true)
    setSubmitError(null)

    try {
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        throw new Error('Invalid email format')
      }

      const { error } = await supabase
        .from('contacts')
        .insert([data])

      if (error) {
        if (error.code === '23505') {
          throw new Error('A contact with this email already exists')
        }
        throw error
      }

      // Success! Reset form and redirect
      reset()
      navigate('/contact-success')
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <FadeIn className="lg:order-last">
      <form name="contact" onSubmit={handleSubmit(onSubmit)}>
        <input type="hidden" name="form-name" value="contact" />
        <h2 className="font-display text-base font-semibold text-neutral-950">Work inquiries</h2>

        {submitError && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{submitError}</p>
          </div>
        )}

        <div className="isolate mt-6 -space-y-px rounded-2xl bg-white/50">
          <TextInput
            label="Name"
            autoComplete="name"
            {...register("name", { required: "required" })}
            error={errors.name?.message}
          />
          <TextInput
            label="Email"
            type="email"
            autoComplete="email"
            {...register("email", { required: "required" })}
            error={errors.email?.message}
          />
          <TextInput
            label="Country / Region"
            {...register("country", { required: "required" })}
            error={errors.country?.message}
          />
          <TextInput
            label="Phone Number (with Country Code)"
            type="tel"
            autoComplete="tel"
            {...register("phone", { required: "required" })}
            error={errors.phone?.message}
          />
          <div className="border border-neutral-300 px-6 py-8 first:rounded-t-2xl last:rounded-b-2xl">
            <fieldset>
              <legend className="text-base/6 text-neutral-500">Preferred method of contact</legend>
              <div className="mt-6 grid grid-cols-1 gap-8 sm:grid-cols-2">
                <RadioInput label="Email" {...register("preference")} value="email" />
                <RadioInput label="Phone" {...register("preference")} value="phone" />
              </div>
            </fieldset>
          </div>
          <TextInput label="Message" {...register("message")} />
        </div>

        <Button
          type="submit"
          className="mt-10"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Get in touch'}
        </Button>
      </form>
    </FadeIn>
  )
}
