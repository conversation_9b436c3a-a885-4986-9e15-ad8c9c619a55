import React, { type ComponentProps, useRef } from "react"
import { motion, useMotionTemplate, useScroll, useTransform } from "motion/react"

const MotionImage = motion.img

export function GrayscaleTransitionImage(
  props: Pick<ComponentProps<"img">, "src"> & {
    alt?: string
  },
) {
  let ref = useRef<React.ComponentRef<"div">>(null)
  let { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start 65%", "end 35%"],
  })
  let grayscale = useTransform(scrollYProgress, [0, 0.5, 1], [1, 0, 1])
  let filter = useMotionTemplate`grayscale(${grayscale})`

  return (
    <div ref={ref} className="group relative">
      <MotionImage alt="" style={{ filter } as any} {...props} />
      <div
        className="pointer-events-none absolute left-0 top-0 w-full opacity-0 transition duration-300 group-hover:opacity-100"
        aria-hidden="true"
      >
        <img alt="" {...props} />
      </div>
    </div>
  )
}
