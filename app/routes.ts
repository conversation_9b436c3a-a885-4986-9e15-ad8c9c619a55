import { index, route, type RouteConfig } from "@react-router/dev/routes"

export default [
  index("routes/home.tsx"),
  route("about", "routes/about.tsx"),
  route("blog", "routes/blog.tsx"),
  // route("blog/*", "routes/blog-wrapper.tsx"),
  route("career", "routes/career.tsx"),
  // route("career/*", "routes/job-opening-wrapper.tsx"),
  route("contact", "routes/contact.tsx"),
  route("contact/success", "routes/contact-success.tsx"),
  route("up", "routes/up.tsx"), // Health check route for <PERSON>
] satisfies RouteConfig
