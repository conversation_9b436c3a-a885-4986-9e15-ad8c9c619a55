import { Border } from "@/components/Border"
import { ContactSection } from "@/components/ContactSection"
import { Container } from "@/components/Container"
import { FadeIn, FadeInStagger } from "@/components/FadeIn"
import { GridList, GridListItem } from "@/components/GridList"
import { PageIntro } from "@/components/PageIntro"
import { SectionIntro } from "@/components/SectionIntro"
import imageAngelaFisher from "@/images/team/angela-fisher.jpg"
import imageBenjaminRussel from "@/images/team/benjamin-russel.jpg"
import imageBlakeReid from "@/images/team/blake-reid.jpg"
import imageChelseaHagon from "@/images/team/chelsea-hagon.jpg"
import imageEmmaDorsey from "@/images/team/emma-dorsey.jpg"
import imageJeffreyWebb from "@/images/team/jeffrey-webb.jpg"
import imageKathrynMurphy from "@/images/team/kathryn-murphy.jpg"
import logoPhobiaDark from "@/images/clients/phobia/logo-dark.svg"
import imageL<PERSON><PERSON><PERSON>ras<PERSON> from "@/images/team/leonard-krasner.jpg"
import imageWhitney<PERSON>ran<PERSON> from "@/images/team/whitney-francis.jpg"
import { Testimonial } from "@/components/Testimonial"
import type { Route } from "./+types/about"

function Culture() {
  return (
    <div className="mt-24 rounded-4xl bg-neutral-950 py-24 sm:mt-32 lg:mt-40 lg:py-32">
      <SectionIntro eyebrow="Our culture" title="Balance your passion with your passion for life." invert>
        <p>We are a group of like-minded people who share the same core values.</p>
      </SectionIntro>
      <Container className="mt-16">
        <GridList>
          <GridListItem title="Vision" invert>
            Our vision is to be the most trusted and respected finance consulting firm, while fostering a positive and
            inclusive work environment for our team.
          </GridListItem>
          <GridListItem title="Values" invert>
            Trust, Transparency, Expertise, Long-term relationships, Integrity, Gratitude, and Work-life balance.
          </GridListItem>
          <GridListItem title="Mission" invert className="sm:col-span-2">
            Our mission is to provide high-quality finance consulting services while prioritizing trust, transparency,
            and building long-term relationships with our clients. We strive to approach our work with an attitude of
            gratitude, always seeking to exceed expectations and deliver exceptional results.
          </GridListItem>
        </GridList>
      </Container>
    </div>
  )
}

const team = [
  {
    title: "MEET THE TEAM",
    people: [
      {
        name: "Nitin Salian",
        role: "Director",
        image: { src: imageChelseaHagon },
      },
      {
        name: "Jasmine Nyein Kyaw",
        role: "Senior Associate",
        image: { src: imageEmmaDorsey },
      },
      {
        name: "Asmita Sitoula",
        role: "Senior Associate",
        image: { src: imageLeonardKrasner },
      },
      {
        name: "Blake Reid",
        role: "Junior Copywriter",
        image: { src: imageBlakeReid },
      },
      {
        name: "Kathryn Murphy",
        role: "VP, Human Resources",
        image: { src: imageKathrynMurphy },
      },
      {
        name: "Whitney Francis",
        role: "Content Specialist",
        image: { src: imageWhitneyFrancis },
      },
      {
        name: "Jeffrey Webb",
        role: "Account Coordinator",
        image: { src: imageJeffreyWebb },
      },
      {
        name: "Benjamin Russel",
        role: "Senior Developer",
        image: { src: imageBenjaminRussel },
      },
      {
        name: "Angela Fisher",
        role: "Front-end Developer",
        image: { src: imageAngelaFisher },
      },
    ],
  },
]

function Team() {
  return (
    <Container className="mt-24 sm:mt-32 lg:mt-40">
      <div className="space-y-24">
        {team.map(group => (
          <FadeInStagger key={group.title}>
            <Border as={FadeIn} />
            <div className="grid grid-cols-1 gap-6 pt-12 sm:pt-16 lg:grid-cols-4 xl:gap-8">
              <FadeIn>
                <h2 className="font-display text-2xl font-semibold text-neutral-950">{group.title}</h2>
              </FadeIn>
              <div className="lg:col-span-3">
                <ul role="list" className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-8">
                  {group.people.map(person => (
                    <li key={person.name}>
                      <FadeIn>
                        <div className="group relative overflow-hidden rounded-3xl bg-neutral-100">
                          <img
                            alt=""
                            {...person.image}
                            className="h-96 w-full object-cover grayscale transition duration-500 motion-safe:group-hover:scale-105"
                          />
                          <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black to-black/0 to-40% p-6">
                            <p className="font-display text-base/6 font-semibold tracking-wide text-white">
                              {person.name}
                            </p>
                            <p className="mt-2 text-sm text-white">{person.role}</p>
                          </div>
                        </div>
                      </FadeIn>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </FadeInStagger>
        ))}
      </div>
    </Container>
  )
}

export function meta({}: Route.MetaArgs) {
  return [
    { title: "About team" },
    {
      name: "description",
      content:
        "Founded in 2019, we are a team of highly experienced finance consulting professionals with a proven track record of providing high-quality services to clients.",
    },
  ]
}

export default function About() {
  return (
    <>
      <PageIntro eyebrow="About us" title="Who We Are">
        <div className="mt-10 max-w-2xl space-y-6 text-base">
          <p>
            Founded in 2019, we are a team of highly experienced finance consulting professionals with a proven track
            record of providing high-quality services to clients. Our extensive knowledge and expertise enable us to
            offer a comprehensive range of tailored accounting and compliance services, corporate services, tax
            consulting, financial due diligence, and valuation, as well as residency and citizenship planning.
          </p>
          <p>
            At our firm, we place the utmost priority on trust and client confidentiality. We work with an approach that
            allows clients to reach us directly, maintain complete transparency in our services and pricing, and value
            building long-term relationships with clients. Our past collaborations with global firms have contributed to
            our growing business, which is a testament to our ability to deliver results and exceed expectations.
          </p>
          <p>
            We understand that choosing a finance consulting provider is a significant decision, and we aim to earn the
            trust and confidence of each of our clients. Whether you are looking to expand your business, optimize your
            finance function, or achieve the best governance and compliance, we are committed to providing the highest
            level of expertise and support to help you achieve your goals.
          </p>
        </div>
      </PageIntro>

      <Culture />

      <Testimonial className="mt-24 sm:mt-32 lg:mt-40" client={{ name: "Phobia", logo: logoPhobiaDark }}>
        Nitin is a very determined worker and when I say worker he is not just any other worker. He is exceptionally
        hard worker and technically very sound. I worked with him on several assignments and always i felt lucky to have
        him on my team. He is one teammate when you delegate him any work you should be rest assured that he will get it
        done and without a need to explain the second time. Plus within assigned time as well. His out of box thinking
        and problem-solving skills will take long way in professional world. I wish him all the best with his career and
        recommend him as an excellent pick for any technical role requiring his qualifications and experience.
      </Testimonial>

      <Team />

      <ContactSection />
    </>
  )
}
