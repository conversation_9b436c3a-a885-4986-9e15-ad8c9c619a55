// import type { Route } from "./+types/blog-wrapper"
// import { allBlogs } from "content-collections"
// import { MDXContent } from "@content-collections/mdx/react"
// import { Container } from "@/components/Container"
// import { FadeIn } from "@/components/FadeIn"
// import { formatDate } from "@/lib/formatDate"
// import { ContactSection } from "@/components/ContactSection"
// import { PageLinks } from "@/components/PageLinks"
// import { useLoaderData } from "react-router"

// export async function loader({ params }: Route.LoaderArgs) {
//   const { "*": splat } = params
//   const blog = allBlogs.find(x => x._meta.path === splat)
//   const moreBlogs = allBlogs.filter(x => x._meta.path !== splat)

//   return { blog, moreBlogs }
// }

// export default function BlogWrapper() {
//   const { blog, moreBlogs } = useLoaderData()
//   return (
//     <>
//       <Container className="mt-24 sm:mt-32 lg:mt-40">
//         <FadeIn>
//           <header className="mx-auto flex max-w-5xl flex-col text-center">
//             <h1 className="mt-6 font-display text-5xl font-medium tracking-tight text-neutral-950 [text-wrap:balance] sm:text-6xl">
//               {blog!.title}
//             </h1>
//             <time dateTime={blog!.date} className="order-first text-sm text-neutral-950">
//               {formatDate(blog!.date)}
//             </time>
//             <p className="mt-6 text-sm font-semibold text-neutral-950">
//               by {blog!.author}, {blog!.role}
//             </p>
//           </header>
//         </FadeIn>

//         <FadeIn>
//           <div className="prose lg:prose-lg mx-auto mt-24 sm:mt-32 lg:mt-40">
//             <MDXContent code={blog!.mdx} />
//           </div>
//         </FadeIn>
//       </Container>

//       {moreBlogs.length > 0 && <PageLinks className="mt-24 sm:mt-32 lg:mt-40" title="More blogs" pages={moreBlogs} />}

//       <ContactSection />
//     </>
//   )
// }
