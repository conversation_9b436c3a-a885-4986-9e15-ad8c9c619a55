import { Border } from "@/components/Border"
import { Button } from "@/components/Button"
import { ContactSection } from "@/components/ContactSection"
import { Container } from "@/components/Container"
import { FadeIn } from "@/components/FadeIn"
import { PageIntro } from "@/components/PageIntro"
import { formatDate } from "@/lib/formatDate"
import { Link } from "react-router"
// import { allBlogs } from "content-collections"
const allBlogs: any[] = []
export default function Blog() {
  return (
    <>
      <PageIntro eyebrow="Blog" title="Our Insights">
        <p>
          We love sharing our insights on the latest changes in the financial world. From analysis and guides to
          training material and interesting insights, we&apos;ve got you covered. Our blog is the perfect place to learn
          more about the financial industry and stay up-to-date with the latest trends. We also share our own takes on
          the industry, providing a unique perspective on everything from investing to taxes.
        </p>
      </PageIntro>
      <Container className="mt-24 sm:mt-32 lg:mt-40">
        <div className="space-y-24 lg:space-y-32">
          {allBlogs.map(article => (
            <FadeIn key={article.href}>
              <article>
                <Border className="pt-16">
                  <div className="relative lg:-mx-4 lg:flex lg:justify-end">
                    <div className="pt-10 lg:w-2/3 lg:flex-none lg:px-4 lg:pt-0">
                      <h2 className="font-display text-2xl font-semibold text-neutral-950">
                        <Link to={article.href}>{article.title}</Link>
                      </h2>
                      <dl className="lg:absolute lg:left-0 lg:top-0 lg:w-1/3 lg:px-4">
                        <dt className="sr-only">Published</dt>
                        <dd className="absolute left-0 top-0 text-sm text-neutral-950 lg:static">
                          <time dateTime={article.date}>{formatDate(article.date)}</time>
                        </dd>
                        <dt className="sr-only">Author</dt>
                        <dd className="mt-6 flex gap-x-4">
                          <div className="flex-none overflow-hidden rounded-xl bg-neutral-100"></div>
                          <div className="text-sm text-neutral-950">
                            <div className="font-semibold">{article.author}</div>
                            <div>{article.role}</div>
                          </div>
                        </dd>
                      </dl>
                      <p className="mt-6 max-w-2xl text-base text-neutral-600">{article.description}</p>
                      <Button href={article.href} aria-label={`Read more: ${article.title}`} className="mt-8">
                        Read more
                      </Button>
                    </div>
                  </div>
                </Border>
              </article>
            </FadeIn>
          ))}
        </div>
      </Container>
      <ContactSection/>
    </>
  )
}
