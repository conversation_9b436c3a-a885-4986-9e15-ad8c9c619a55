import { Border } from "@/components/Border"
import { But<PERSON> } from "@/components/Button"
import { ContactSection } from "@/components/ContactSection"
import { Container } from "@/components/Container"
import { FadeIn } from "@/components/FadeIn"
import { PageIntro } from "@/components/PageIntro"
import { Link } from "react-router"
// import { allJobOpenings } from "content-collections"
const allJobOpenings: any[] = []

function JobOpenings() {
  return (
    <>
      <Container className="mt-40">
        <FadeIn>
          <h2 className="font-display text-2xl font-semibold text-neutral-950">Job Openings</h2>
        </FadeIn>
        <div className="mt-10 space-y-20 sm:space-y-24 lg:space-y-32">
          {allJobOpenings.map(jobOpening => (
            <FadeIn key={jobOpening.title}>
              <article>
                <Border className="grid grid-cols-3 gap-x-8 gap-y-8 pt-16">
                  <div className="col-span-full lg:col-span-2 lg:max-w-2xl">
                    <p className="font-display text-4xl font-medium text-neutral-950">
                      <Link to={jobOpening.href}>{jobOpening.title}</Link>
                    </p>
                    <div className="mt-6 space-y-6 text-base text-neutral-600">
                      <p>{jobOpening.summary}</p>
                    </div>
                    <div className="mt-8 flex">
                      <Button href={jobOpening.href} aria-label={`Apply Job now`}>
                        Apply Now !
                      </Button>
                    </div>
                  </div>
                </Border>
              </article>
            </FadeIn>
          ))}
        </div>
      </Container>
    </>
  )
}

export default function Work() {
  return (
    <>
      <PageIntro eyebrow="Career" title="Let Your Aspirations Meet Opportunities">
        <p>
          Are you seeking an opportunity to advance your career and work alongside a dynamic team of professionals?
          Explore our current job openings and be part of our growing team.
        </p>
        <br/>
        <p>
          We are a dynamic and innovative young company that offers ample training and development opportunities to our
          team members. At our company, you will have the chance to take initiative and build the business from the
          ground up. Our team members are kind, supportive, and collaborative, and we value work-life balance, health,
          and personal life as much as we value hard work and dedication. Joining us means that you will have the chance
          to work with clients who are industry experts and gain exposure to the financial services industry. You will
          also get to work in a cool and fun work environment where creativity and innovation are encouraged. Our
          growing firm offers exciting career paths and opportunities for personal and professional growth. So, if you
          are looking for a fulfilling career with a company that values its team members and offers a fun and dynamic
          work culture, look no further!
        </p>
      </PageIntro>
      <JobOpenings/>
      <ContactSection/>
    </>
  )
}
