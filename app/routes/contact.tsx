import { Border } from "@/components/Border"
import { Container } from "@/components/Container"
import { FadeIn } from "@/components/FadeIn"
import { Offices } from "@/components/Offices"
import { PageIntro } from "@/components/PageIntro"
import { ContactForm } from "@/components/ContactForm"
import { Link } from "react-router"

function ContactDetails() {
  return (
    <FadeIn>
      <h2 className="font-display text-base font-semibold text-neutral-950">Our offices</h2>
      <p className="mt-6 text-base text-neutral-600">
        Prefer doing things in person? Contact us today to learn more about how we can help you navigate the financial
        landscape and maximize your potential.
      </p>

      <Offices className="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2" />

      <Border className="mt-16 pt-16">
        <h2 className="font-display text-base font-semibold text-neutral-950">Email us</h2>
        <dl className="mt-6 grid grid-cols-1 gap-8 text-sm sm:grid-cols-2">
          {[
            ["Careers", "<EMAIL>"],
            ["Press", "<EMAIL>"],
          ].map(([label, email]) => (
            <div key={email}>
              <dt className="font-semibold text-neutral-950">{label}</dt>
              <dd>
                <Link to={`mailto:${email}`} className="text-neutral-600 hover:text-neutral-950">
                  {email}
                </Link>
              </dd>
            </div>
          ))}
        </dl>
      </Border>
    </FadeIn>
  )
}

export const metadata = {
  title: "Contact Us",
  description: "Get In Touch. We can’t wait to hear from you.",
}

export default function Contact() {
  return (
    <>
      <PageIntro eyebrow="Contact us" title="Get In Touch">
        <p>For more information about us and our services, please provide your details below.</p>
      </PageIntro>

      <Container className="mt-24 sm:mt-32 lg:mt-40">
        <div className="grid grid-cols-1 gap-x-8 gap-y-24 lg:grid-cols-2">
          <ContactForm />
          <ContactDetails />
        </div>
      </Container>
    </>
  )
}
