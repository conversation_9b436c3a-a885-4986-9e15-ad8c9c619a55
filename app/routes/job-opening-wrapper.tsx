// import type { Route } from "./+types/job-opening-wrapper"
// import { allJobOpenings } from "content-collections"
// import { PageIntro } from "@/components/PageIntro"
// import { Container } from "@/components/Container"
// import { FadeIn } from "@/components/FadeIn"
// import { MDXContent } from "@content-collections/mdx/react"
// import { PageLinks } from "@/components/PageLinks"
// import { ContactSection } from "@/components/ContactSection"
// import { useLoaderData } from "react-router"

// export async function loader({ params }: Route.LoaderArgs) {
//   const { "*": splat } = params
//   const jobOpening = allJobOpenings.find(x => x._meta.path === splat)

//   const moreJobOpenings = allJobOpenings.filter(x => x._meta.path !== splat)

//   return { jobOpening, moreJobOpenings }
// }

// export default function JobOpeningWrapper() {
//   const { jobOpening, moreJobOpenings } = useLoaderData()

//   return (
//     <>
//       <article className="mt-24 sm:mt-32 lg:mt-40">
//         <header>
//           <PageIntro eyebrow="Job Openings" title={jobOpening.title} centered>
//             <p>{jobOpening.description}</p>
//           </PageIntro>
//         </header>

//         <Container className="mt-24 sm:mt-32 lg:mt-40">
//           <FadeIn>
//             <div className="prose lg:prose-lg mx-auto mt-24 sm:mt-32 lg:mt-40">
//               <MDXContent code={jobOpening!.mdx} />
//             </div>
//           </FadeIn>
//         </Container>
//       </article>

//       {moreJobOpenings.length > 0 && (
//         <PageLinks className="mt-24 sm:mt-32 lg:mt-40" title="More Job Openings" pages={moreJobOpenings} />
//       )}

//       <ContactSection />
//     </>
//   )
// }
