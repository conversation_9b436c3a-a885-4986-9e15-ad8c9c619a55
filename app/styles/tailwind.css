@import "tailwindcss";
@import "./base.css";
@import "./typography.css" layer(components);

@theme {
  --text-*: initial;
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.5rem;
  --text-base: 1rem;
  --text-base--line-height: 1.75rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 2rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2.25rem;
  --text-3xl: 1.75rem;
  --text-3xl--line-height: 2.25rem;
  --text-4xl: 2rem;
  --text-4xl--line-height: 2.5rem;
  --text-5xl: 2.5rem;
  --text-5xl--line-height: 3rem;
  --text-6xl: 3rem;
  --text-6xl--line-height: 3.5rem;
  --text-7xl: 4rem;
  --text-7xl--line-height: 4.5rem;

  --radius-4xl: 2.5rem;

  --font-sans:
    Mona Sans, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --font-display:
    Mona Sans, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --font-display--font-variation-settings: "wdth" 125;

  --font-display-length: 2;
}
