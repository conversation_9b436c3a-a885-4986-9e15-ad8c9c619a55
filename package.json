{"name": "thanks-nitin", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "PORT=4000 react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "prettier": "prettier . --write", "clean": "rm -rf node_modules pnpm-lock.yaml", "reinstall": "pnpm install"}, "dependencies": {"@react-router/node": "^7.8.2", "@react-router/serve": "^7.8.2", "@supabase/supabase-js": "^2.56.0", "clsx": "^2.1.1", "isbot": "^5.1.30", "motion": "^12.23.12", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-router": "^7.8.2"}, "devDependencies": {"@react-router/dev": "^7.8.2", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.12", "@types/node": "^20.19.11", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "react-router-devtools": "^5.0.7", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite": "^7.1.3", "vite-tsconfig-paths": "^5.1.4"}, "pnpm": {"approvedBuilds": {"@tailwindcss/oxide": true, "esbuild": true}}, "volta": {"node": "22.14.0", "pnpm": "10.15.0"}}