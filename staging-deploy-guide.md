# Complete Step-by-Step Guide: Deploy Staging Environment with Kamal 2

## **Step 1: Create Staging Configuration File**

Create a new file `config/deploy.staging.yml`:

```yaml
# config/deploy.staging.yml
service: thanks-nitin-staging
image: ethanliu1230/thanks-nitin:staging

servers:
  web:
    - ************

proxy:
  ssl: true
  host: staging.nitin.my
  app_port: 4001

registry:
  username: ethanliu1230
  password:
    - KAMAL_REGISTRY_PASSWORD

builder:
  arch: amd64
  args:
    BUILDKIT_INLINE_CACHE: 1
```

## **Step 2: Configure Cloudflare DNS**

1. **Log into Cloudflare** dashboard
2. **Select your `nitin.my` domain**
3. **Go to DNS tab**
4. **Click "Add record"**
5. **Configure the record**:
   - **Type**: `A`
   - **Name**: `staging`
   - **IPv4 address**: `************`
   - **Proxy status**: `DNS only` (gray cloud)
6. **Click "Save"**

## **Step 3: Verify SSL/TLS Settings**

1. **In Cloudflare, go to SSL/TLS tab**
2. **Ensure encryption mode is set to "Full"**
3. **Save if changed**

## **Step 4: Build and Push Staging Docker Image**

```bash
# Build staging image with staging tag
docker build -t ethanliu1230/thanks-nitin:staging .

# Push to Docker Hub
docker push ethanliu1230/thanks-nitin:staging
```

## **Step 5: Deploy Staging Environment**

```bash
# Deploy using staging config
kamal deploy --config config/deploy.staging.yml
```

## **Step 6: Verify Deployment**

1. **Check if staging is running**:
   ```bash
   kamal app status --config config/deploy.staging.yml
   ```

2. **Check logs if needed**:
   ```bash
   kamal app logs --config config/deploy.staging.yml
   ```

3. **Visit `https://staging.nitin.my`** in your browser

## **Step 7: Test Both Environments**

- **Production**: `https://nitin.my` (port 4000)
- **Staging**: `https://staging.nitin.my` (port 4001)

## **Step 8: Future Deployments**

**For production**:
```bash
kamal deploy
```

**For staging**:
```bash
kamal deploy --config config/deploy.staging.yml
```

## **Troubleshooting Commands**

```bash
# Check staging service status
kamal app status --config config/deploy.staging.yml

# View staging logs
kamal app logs --config config/deploy.staging.yml

# SSH into staging container
kamal app exec --config config/deploy.staging.yml --interactive --reuse bash

# Restart staging service
kamal app restart --config config/deploy.staging.yml
```

## **Key Points**

- **Different service names**: `thanks-nitin` vs `thanks-nitin-staging`
- **Different ports**: `4000` vs `4001` (prevents conflicts)
- **Different domains**: `nitin.my` vs `staging.nitin.my`
- **Same VPS**: Both run on `************`
- **Separate SSL certificates**: Kamal handles both automatically

This setup gives you completely isolated production and staging environments that can run simultaneously without conflicts!